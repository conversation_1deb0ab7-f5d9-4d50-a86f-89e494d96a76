{"installCommand": "npm install --legacy-peer-deps", "buildCommand": "sh build.sh", "outputDirectory": "dist", "rewrites": [{"source": "/property/:id/:subpath", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/property/:id/:subpath"}, {"source": "/property/:id/:subpath/:path*", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/property/:id/:subpath/:path*"}, {"source": "/lease", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/lease"}, {"source": "/lease/:id", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/lease/:id"}, {"source": "/lease/:id/:subpath", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/lease/:id/:subpath"}, {"source": "/_next/:path*", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/_next/:path*"}, {"source": "/images/:path*", "destination": "https://cnc-odin-git-development-nantucket-rentals.vercel.app/images/:path*"}, {"source": "/(.*)", "destination": "/"}]}